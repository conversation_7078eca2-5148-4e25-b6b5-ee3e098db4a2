// pages/quoteDetail/quoteDetail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phoneModels: [
      {
        id: 1,
        name: '靓机/小花',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 2,
        name: '花机/内爆',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 3,
        name: '卡贴外版',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 4,
        name: '外版无锁',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      }
    ],
    conditionOptions: ['按金额下调', '按比例下调（自动取整十数）'],
    colorOptions: [
      { id: 0, name: '高级黑', gradient: 'linear-gradient(135deg, #000000, #b3b3b3)' },
      { id: 1, name: '尊贵金', gradient: 'linear-gradient(135deg, #b8860b, #f2d78b)' },
      { id: 2, name: '橄榄绿', gradient: 'linear-gradient(135deg, #90ac71, #90ac71)' },
      { id: 3, name: '通用蓝', gradient: 'linear-gradient(135deg, #72a1d5, #82b8f8)' },
      { id: 4, name: '活力橙', gradient: 'linear-gradient(135deg, #ff8c00, #ff6347)' },
      { id: 5, name: '简约白', gradient: 'linear-gradient(135deg, #ffffff, #ffffff)' },
    ],
    selectedColorId: 0, // 默认选中高级黑
    watermarkText: '输入您的水印名称',

    // iPhone型号选择相关数据
    showModelSelector: false, // 是否显示型号选择器
    selectedModelText: 'iPhone 16 Pro', // 当前选中的型号显示文本
    selectedModels: [], // 已选中的型号列表
    iPhoneModels: [
      // iPhone 16 系列
      { id: 'iphone16', name: 'iPhone 16', selected: false, category: 'iPhone 16' },
      { id: 'iphone16pro', name: 'iPhone 16 Pro', selected: true, category: 'iPhone 16' },
      { id: 'iphone16promax', name: 'iPhone 16 Pro Max', selected: false, category: 'iPhone 16' },
      { id: 'iphone16plus', name: 'iPhone 16 Plus', selected: false, category: 'iPhone 16' },

      // iPhone 15 系列
      { id: 'iphone15', name: 'iPhone 15', selected: false, category: 'iPhone 15' },
      { id: 'iphone15plus', name: 'iPhone 15 Plus', selected: false, category: 'iPhone 15' },
      { id: 'iphone15pro', name: 'iPhone 15 Pro', selected: false, category: 'iPhone 15' },
      { id: 'iphone15promax', name: 'iPhone 15 Pro Max', selected: false, category: 'iPhone 15' },

      // iPhone 14 系列
      { id: 'iphone14', name: 'iPhone 14', selected: false, category: 'iPhone 14' },
      { id: 'iphone14plus', name: 'iPhone 14 Plus', selected: false, category: 'iPhone 14' },
      { id: 'iphone14pro', name: 'iPhone 14 Pro', selected: false, category: 'iPhone 14' },
      { id: 'iphone14promax', name: 'iPhone 14 Pro Max', selected: false, category: 'iPhone 14' },

      // iPhone 13 系列
      { id: 'iphone13mini', name: 'iPhone 13 mini', selected: false, category: 'iPhone 13' },
      { id: 'iphone13', name: 'iPhone 13', selected: false, category: 'iPhone 13' },
      { id: 'iphone13pro', name: 'iPhone 13 Pro', selected: false, category: 'iPhone 13' },
      { id: 'iphone13promax', name: 'iPhone 13 Pro Max', selected: false, category: 'iPhone 13' },

      // iPhone 12 系列
      { id: 'iphone12mini', name: 'iPhone 12 mini', selected: false, category: 'iPhone 12' },
      { id: 'iphone12', name: 'iPhone 12', selected: false, category: 'iPhone 12' },
      { id: 'iphone12pro', name: 'iPhone 12 Pro', selected: false, category: 'iPhone 12' },
      { id: 'iphone12promax', name: 'iPhone 12 Pro Max', selected: false, category: 'iPhone 12' },

      // iPhone 11 系列
      { id: 'iphone11', name: 'iPhone 11', selected: false, category: 'iPhone 11' },
      { id: 'iphone11pro', name: 'iPhone 11 Pro', selected: false, category: 'iPhone 11' },
      { id: 'iphone11promax', name: 'iPhone 11 Pro Max', selected: false, category: 'iPhone 11' }
    ],

    // 价格配置相关数据
    priceConfigs: [
      { id: 'xiahua100', name: '小花-单机100🔒 在保100+', price: 8000, unit: '标记优势', selected: false },
      { id: 'ziyuanji20', name: '资源机20次内充新', price: 8000, unit: '标记优势', selected: false },
      { id: 'gaopei50', name: '高配靓充50次内在保280+', price: 9450, unit: '标记优势', selected: false }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化选中的型号列表
    this.initializeSelectedModels();
  },

  // 初始化选中的型号列表
  initializeSelectedModels() {
    const selectedModels = this.data.iPhoneModels.filter(model => model.selected);
    this.setData({
      selectedModels: selectedModels
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 处理水印输入
  onWatermarkInput(e) {
    this.setData({
      watermarkText: e.detail.value
    });
  },

  // 颜色选择处理
  onColorSelect(e) {
    const colorId = e.currentTarget.dataset.colorId;
    this.setData({
      selectedColorId: colorId
    });

    // 保存选中的颜色到云数据库（可选）
    const selectedColor = this.data.colorOptions[colorId];
    console.log('选中颜色:', selectedColor);
  },

  // 手机型号选择框点击
  onPhoneSelect(e) {
    const index = e.currentTarget.dataset.index;
    const phoneModels = this.data.phoneModels;
    phoneModels[index].selected = !phoneModels[index].selected;

    this.setData({
      phoneModels: phoneModels
    });

    console.log('手机型号选择状态:', phoneModels[index]);
  },

  // 条件选择器点击
  onConditionSelect(e) {
    const index = e.currentTarget.dataset.index;
    const phoneModels = this.data.phoneModels;

    // 切换下拉框显示状态
    phoneModels[index].showDropdown = !phoneModels[index].showDropdown;

    // 关闭其他下拉框
    phoneModels.forEach((item, i) => {
      if (i !== index) {
        item.showDropdown = false;
      }
    });

    this.setData({
      phoneModels: phoneModels
    });
  },

  // 选择条件选项
  onConditionOptionSelect(e) {
    const phoneIndex = e.currentTarget.dataset.phoneIndex;
    const optionIndex = e.currentTarget.dataset.optionIndex;
    const phoneModels = this.data.phoneModels;

    // 更新选中的条件
    phoneModels[phoneIndex].condition = this.data.conditionOptions[optionIndex];
    phoneModels[phoneIndex].showDropdown = false;

    this.setData({
      phoneModels: phoneModels
    });

    console.log('条件选择:', phoneModels[phoneIndex]);
  },

  // iPhone型号选择相关方法

  // 显示/隐藏型号选择器
  toggleModelSelector() {
    this.setData({
      showModelSelector: !this.data.showModelSelector
    });
  },

  // 选择/取消选择iPhone型号
  onModelSelect(e) {
    const modelId = e.currentTarget.dataset.modelId;
    const iPhoneModels = [...this.data.iPhoneModels];

    // 找到对应的型号并切换选中状态
    const modelIndex = iPhoneModels.findIndex(model => model.id === modelId);
    if (modelIndex !== -1) {
      iPhoneModels[modelIndex].selected = !iPhoneModels[modelIndex].selected;
    }

    // 更新选中的型号列表和显示文本
    this.updateSelectedModels(iPhoneModels);
  },

  // 更新选中型号列表和显示文本
  updateSelectedModels(iPhoneModels) {
    const selectedModels = iPhoneModels.filter(model => model.selected);
    let selectedModelText = '';

    if (selectedModels.length === 0) {
      selectedModelText = 'iPhone 16 Pro'; // 默认显示
    } else if (selectedModels.length === 1) {
      selectedModelText = selectedModels[0].name;
    } else {
      selectedModelText = `${selectedModels[0].name}...`; // 保持选项框长度不变
    }

    this.setData({
      iPhoneModels: iPhoneModels,
      selectedModels: selectedModels,
      selectedModelText: selectedModelText
    });
  },

  // 清空所有选择
  clearAllSelections() {
    const iPhoneModels = this.data.iPhoneModels.map(model => ({
      ...model,
      selected: false
    }));

    this.setData({
      iPhoneModels: iPhoneModels,
      selectedModels: [],
      selectedModelText: 'iPhone 16 Pro'
    });
  },

  // 确认已选型号
  confirmSelectedModels() {
    const { selectedModels } = this.data;

    if (selectedModels.length === 0) {
      wx.showToast({
        title: '请至少选择一个型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 关闭选择器
    this.setData({
      showModelSelector: false
    });

    // 这里可以处理确认后的逻辑
    console.log('确认选择的型号:', selectedModels);

    wx.showToast({
      title: `已选择${selectedModels.length}个型号`,
      icon: 'success',
      duration: 1500
    });
  },

  // 价格配置选择
  onPriceConfigSelect(e) {
    const configId = e.currentTarget.dataset.configId;
    const priceConfigs = [...this.data.priceConfigs];

    // 找到对应的配置并切换选中状态
    const configIndex = priceConfigs.findIndex(config => config.id === configId);
    if (configIndex !== -1) {
      priceConfigs[configIndex].selected = !priceConfigs[configIndex].selected;
    }

    this.setData({
      priceConfigs: priceConfigs
    });

    console.log('价格配置选择:', priceConfigs[configIndex]);
  },

  // 个性化调整按钮点击
  onPersonalizeAdjust() {
    wx.navigateTo({
      url: '/pages/personalizeAdjust/personalizeAdjust'
    })
  },

  // 生成报价单按钮点击
  onGenerateQuote() {
    const {
      selectedColorId,
      colorOptions,
      watermarkText,
      phoneModels,
      selectedModels,
      priceConfigs
    } = this.data;

    const selectedColor = colorOptions[selectedColorId];
    const selectedPriceConfigs = priceConfigs.filter(config => config.selected);

    console.log('生成报价单参数:', {
      selectedColor,
      watermarkText,
      phoneModels,
      selectediPhoneModels: selectedModels,
      selectedPriceConfigs
    });

    // 验证是否有选择的内容
    if (selectedModels.length === 0) {
      wx.showToast({
        title: '请先选择iPhone型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 这里可以调用云函数生成报价单图片
    wx.showLoading({
      title: '生成中...'
    });

    // 模拟生成过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '报价单生成成功',
        icon: 'success',
        duration: 2000
      });
    }, 1500);
  }
})
