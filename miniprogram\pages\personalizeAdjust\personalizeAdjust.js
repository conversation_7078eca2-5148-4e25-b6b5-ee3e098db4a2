// pages/personalizeAdjust/personalizeAdjust.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: ['靓机/小花', '花机/内爆', '卡贴外版', '外版无锁'],
    activeTab: 0,
    selectedModels: [],
    selectedModelsText: '',
    showDropdown: false,

    // 原有的下拉选择型号选项（保持兼容性）
    modelOptions: [
      { name: 'iPhone 14 Pro Max 256GB', selected: false },
      { name: 'iPhone 14 Pro 128GB', selected: false },
      { name: 'iPhone 14 Plus 256GB', selected: false },
      { name: 'iPhone 14 128GB', selected: false },
      { name: 'iPhone 13 Pro Max 256GB', selected: false },
      { name: 'iPhone 13 Pro 128GB', selected: false },
      { name: 'iPhone 13 256GB', selected: false },
      { name: 'iPhone 13 128GB', selected: false },
      { name: 'iPhone 12 Pro Max 256GB', selected: false },
      { name: 'iPhone 12 Pro 128GB', selected: false }
    ],

    // 新增：iPhone型号网格选择数据
    showModelGrid: false, // 是否显示型号网格选择器
    selectedGridModels: [], // 网格选择的型号列表
    selectedGridModelsText: 'iPhone 16 Pro', // 网格选择显示文本（固定长度）

    // iPhone型号网格数据
    iPhoneGridModels: [
      // iPhone 16 系列
      { id: 'iphone16', name: 'iPhone 16', selected: false, series: 'iPhone 16' },
      { id: 'iphone16pro', name: 'iPhone 16 Pro', selected: true, series: 'iPhone 16' },
      { id: 'iphone16promax', name: 'iPhone 16 Pro Max', selected: false, series: 'iPhone 16' },
      { id: 'iphone16plus', name: 'iPhone 16 Plus', selected: false, series: 'iPhone 16' },

      // iPhone 15 系列
      { id: 'iphone15', name: 'iPhone 15', selected: false, series: 'iPhone 15' },
      { id: 'iphone15plus', name: 'iPhone 15 Plus', selected: false, series: 'iPhone 15' },
      { id: 'iphone15pro', name: 'iPhone 15 Pro', selected: false, series: 'iPhone 15' },
      { id: 'iphone15promax', name: 'iPhone 15 Pro Max', selected: false, series: 'iPhone 15' },

      // iPhone 14 系列
      { id: 'iphone14', name: 'iPhone 14', selected: false, series: 'iPhone 14' },
      { id: 'iphone14plus', name: 'iPhone 14 Plus', selected: false, series: 'iPhone 14' },
      { id: 'iphone14pro', name: 'iPhone 14 Pro', selected: false, series: 'iPhone 14' },
      { id: 'iphone14promax', name: 'iPhone 14 Pro Max', selected: false, series: 'iPhone 14' },

      // iPhone 13 系列
      { id: 'iphone13mini', name: 'iPhone 13 mini', selected: false, series: 'iPhone 13' },
      { id: 'iphone13', name: 'iPhone 13', selected: false, series: 'iPhone 13' },
      { id: 'iphone13pro', name: 'iPhone 13 Pro', selected: false, series: 'iPhone 13' },
      { id: 'iphone13promax', name: 'iPhone 13 Pro Max', selected: false, series: 'iPhone 13' },

      // iPhone 12 系列
      { id: 'iphone12mini', name: 'iPhone 12 mini', selected: false, series: 'iPhone 12' },
      { id: 'iphone12', name: 'iPhone 12', selected: false, series: 'iPhone 12' },
      { id: 'iphone12pro', name: 'iPhone 12 Pro', selected: false, series: 'iPhone 12' },
      { id: 'iphone12promax', name: 'iPhone 12 Pro Max', selected: false, series: 'iPhone 12' },

      // iPhone 11 系列
      { id: 'iphone11', name: 'iPhone 11', selected: false, series: 'iPhone 11' },
      { id: 'iphone11pro', name: 'iPhone 11 Pro', selected: false, series: 'iPhone 11' },
      { id: 'iphone11promax', name: 'iPhone 11 Pro Max', selected: false, series: 'iPhone 11' }
    ],

    // 价格配置数据
    priceConfigs: [
      {
        id: 'xiahua100',
        name: '小花-单机100🔒 在保100+',
        price: 8000,
        unit: '标记优势',
        selected: false
      },
      {
        id: 'ziyuanji20',
        name: '资源机20次内充新',
        price: 8000,
        unit: '标记优势',
        selected: false
      },
      {
        id: 'gaopei50',
        name: '高配靓充50次内在保280+',
        price: 9450,
        unit: '标记优势',
        selected: false
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以接收从上一页传递的参数
    if (options.activeTab) {
      this.setData({
        activeTab: parseInt(options.activeTab)
      });
    }

    // 初始化选中的型号列表
    this.initializeSelectedModels();
  },

  /**
   * 初始化选中的型号列表
   */
  initializeSelectedModels() {
    const selectedModels = this.data.modelOptions.filter(model => model.selected);
    const selectedGridModels = this.data.iPhoneGridModels.filter(model => model.selected);

    this.setData({
      selectedModels: selectedModels.map(model => model.name),
      selectedGridModels: selectedGridModels
    });
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    // 重置所有选项为未选中状态
    const modelOptions = this.data.modelOptions.map(item => ({
      ...item,
      selected: false
    }));

    // 重置网格选择
    const iPhoneGridModels = this.data.iPhoneGridModels.map(item => ({
      ...item,
      selected: false
    }));

    // 重置价格配置选择
    const priceConfigs = this.data.priceConfigs.map(item => ({
      ...item,
      selected: false
    }));

    this.setData({
      activeTab: index,
      selectedModels: [],
      selectedModelsText: '',
      showDropdown: false,
      modelOptions: modelOptions,
      // 重置网格相关数据
      selectedGridModels: [],
      selectedGridModelsText: 'iPhone 16 Pro',
      showModelGrid: false,
      iPhoneGridModels: iPhoneGridModels,
      priceConfigs: priceConfigs
    });
  },

  /**
   * 切换下拉框显示状态
   */
  toggleDropdown() {
    this.setData({
      showDropdown: !this.data.showDropdown
    });
  },

  /**
   * 切换型号选择状态（多选）
   */
  toggleModel(e) {
    const index = e.currentTarget.dataset.index;
    const modelOptions = [...this.data.modelOptions];

    // 切换选中状态
    modelOptions[index].selected = !modelOptions[index].selected;

    // 更新选中的型号列表
    const selectedModels = modelOptions.filter(item => item.selected).map(item => item.name);

    // 生成显示文本
    let selectedModelsText = '';
    if (selectedModels.length === 0) {
      selectedModelsText = '';
    } else if (selectedModels.length === 1) {
      selectedModelsText = selectedModels[0];
    } else {
      selectedModelsText = `已选择${selectedModels.length}个型号`;
    }

    this.setData({
      modelOptions: modelOptions,
      selectedModels: selectedModels,
      selectedModelsText: selectedModelsText
    });
  },

  // 新增：iPhone型号网格选择相关方法

  /**
   * 显示/隐藏型号网格选择器
   */
  toggleModelGrid() {
    this.setData({
      showModelGrid: !this.data.showModelGrid
    });
  },

  /**
   * 网格型号选择/取消选择
   */
  onGridModelSelect(e) {
    const modelId = e.currentTarget.dataset.modelId;
    const iPhoneGridModels = [...this.data.iPhoneGridModels];

    // 找到对应的型号并切换选中状态
    const modelIndex = iPhoneGridModels.findIndex(model => model.id === modelId);
    if (modelIndex !== -1) {
      iPhoneGridModels[modelIndex].selected = !iPhoneGridModels[modelIndex].selected;
    }

    // 更新选中的型号列表和显示文本
    this.updateGridSelectedModels(iPhoneGridModels);
  },

  /**
   * 更新网格选中型号列表和显示文本（保持选项框长度不变）
   */
  updateGridSelectedModels(iPhoneGridModels) {
    const selectedGridModels = iPhoneGridModels.filter(model => model.selected);
    let selectedGridModelsText = '';

    if (selectedGridModels.length === 0) {
      selectedGridModelsText = 'iPhone 16 Pro'; // 默认显示，保持长度
    } else if (selectedGridModels.length === 1) {
      selectedGridModelsText = selectedGridModels[0].name;
    } else {
      // 多选时显示固定格式，保持选项框长度不变
      selectedGridModelsText = `${selectedGridModels[0].name}...`;
    }

    this.setData({
      iPhoneGridModels: iPhoneGridModels,
      selectedGridModels: selectedGridModels,
      selectedGridModelsText: selectedGridModelsText
    });
  },

  /**
   * 清空网格所有选择
   */
  clearGridSelections() {
    const iPhoneGridModels = this.data.iPhoneGridModels.map(model => ({
      ...model,
      selected: false
    }));

    this.setData({
      iPhoneGridModels: iPhoneGridModels,
      selectedGridModels: [],
      selectedGridModelsText: 'iPhone 16 Pro'
    });
  },

  /**
   * 确认网格选择的型号
   */
  confirmGridModels() {
    const { selectedGridModels } = this.data;

    if (selectedGridModels.length === 0) {
      wx.showToast({
        title: '请至少选择一个型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 关闭选择器
    this.setData({
      showModelGrid: false
    });

    console.log('确认选择的网格型号:', selectedGridModels);

    wx.showToast({
      title: `已选择${selectedGridModels.length}个型号`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 价格配置选择
   */
  onPriceConfigSelect(e) {
    const configId = e.currentTarget.dataset.configId;
    const priceConfigs = [...this.data.priceConfigs];

    // 找到对应的配置并切换选中状态
    const configIndex = priceConfigs.findIndex(config => config.id === configId);
    if (configIndex !== -1) {
      priceConfigs[configIndex].selected = !priceConfigs[configIndex].selected;
    }

    this.setData({
      priceConfigs: priceConfigs
    });

    console.log('价格配置选择:', priceConfigs[configIndex]);
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生成报价单
   */
  generateQuote() {
    const {
      activeTab,
      selectedModels,
      selectedGridModels,
      priceConfigs,
      tabList
    } = this.data;

    const selectedPriceConfigs = priceConfigs.filter(config => config.selected);

    // 验证选择（优先检查网格选择，如果没有则检查下拉选择）
    const hasGridSelection = selectedGridModels.length > 0;
    const hasDropdownSelection = selectedModels.length > 0;
    const hasPriceSelection = selectedPriceConfigs.length > 0;

    if (!hasGridSelection && !hasDropdownSelection) {
      wx.showToast({
        title: '请先选择型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('生成报价单参数:', {
      activeTab: tabList[activeTab],
      dropdownSelectedModels: selectedModels,
      gridSelectedModels: selectedGridModels,
      selectedPriceConfigs: selectedPriceConfigs,
      hasGridSelection,
      hasDropdownSelection,
      hasPriceSelection
    });

    // 这里可以调用云函数生成报价单
    wx.showLoading({
      title: '生成中...'
    });

    // 模拟生成过程
    setTimeout(() => {
      wx.hideLoading();

      const selectedCount = hasGridSelection ? selectedGridModels.length : selectedModels.length;
      const configCount = selectedPriceConfigs.length;

      let successMessage = `已生成${selectedCount}个型号的报价单`;
      if (configCount > 0) {
        successMessage += `，包含${configCount}个价格配置`;
      }

      wx.showToast({
        title: successMessage,
        icon: 'success',
        duration: 2000
      });

      // 可以跳转到结果页面或返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }, 1500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
