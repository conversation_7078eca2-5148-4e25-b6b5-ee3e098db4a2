// pages/personalizeAdjust/personalizeAdjust.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: ['靓机/小花', '花机/内爆', '卡贴外版', '外版无锁'],
    activeTab: 0,
    selectedModels: [],
    selectedModelsText: '',
    showDropdown: false,
    modelOptions: [
      { name: 'iPhone 14 Pro Max 256GB', selected: false },
      { name: 'iPhone 14 Pro 128GB', selected: false },
      { name: 'iPhone 14 Plus 256GB', selected: false },
      { name: 'iPhone 14 128GB', selected: false },
      { name: 'iPhone 13 Pro Max 256GB', selected: false },
      { name: 'iPhone 13 Pro 128GB', selected: false },
      { name: 'iPhone 13 256GB', selected: false },
      { name: 'iPhone 13 128GB', selected: false },
      { name: 'iPhone 12 Pro Max 256GB', selected: false },
      { name: 'iPhone 12 Pro 128GB', selected: false }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以接收从上一页传递的参数
    if (options.activeTab) {
      this.setData({
        activeTab: parseInt(options.activeTab)
      });
    }
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    // 重置所有选项为未选中状态
    const modelOptions = this.data.modelOptions.map(item => ({
      ...item,
      selected: false
    }));

    this.setData({
      activeTab: index,
      selectedModels: [],
      selectedModelsText: '',
      showDropdown: false,
      modelOptions: modelOptions
    });
  },

  /**
   * 切换下拉框显示状态
   */
  toggleDropdown() {
    this.setData({
      showDropdown: !this.data.showDropdown
    });
  },

  /**
   * 切换型号选择状态（多选）
   */
  toggleModel(e) {
    const index = e.currentTarget.dataset.index;
    const modelOptions = [...this.data.modelOptions];

    // 切换选中状态
    modelOptions[index].selected = !modelOptions[index].selected;

    // 更新选中的型号列表
    const selectedModels = modelOptions.filter(item => item.selected).map(item => item.name);

    // 生成显示文本
    let selectedModelsText = '';
    if (selectedModels.length === 0) {
      selectedModelsText = '';
    } else if (selectedModels.length === 1) {
      selectedModelsText = selectedModels[0];
    } else {
      selectedModelsText = `已选择${selectedModels.length}个型号`;
    }

    this.setData({
      modelOptions: modelOptions,
      selectedModels: selectedModels,
      selectedModelsText: selectedModelsText
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生成报价单
   */
  generateQuote() {
    const { activeTab, selectedModels, tabList } = this.data;

    if (selectedModels.length === 0) {
      wx.showToast({
        title: '请先选择型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 这里可以调用云函数生成报价单
    wx.showLoading({
      title: '生成中...'
    });

    // 模拟生成过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '报价单生成成功',
        icon: 'success',
        duration: 2000
      });
      
      // 可以跳转到结果页面或返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }, 1500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
