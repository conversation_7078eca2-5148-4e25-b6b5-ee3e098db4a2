// pages/personalizeAdjust/personalizeAdjust.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: ['靓机/小花', '花机/内爆', '卡贴外版', '外版无锁'],
    activeTab: 0,
    selectedModel: '',
    showDropdown: false,
    modelOptions: [
      'iPhone 14 Pro Max 256GB',
      'iPhone 14 Pro 128GB',
      'iPhone 14 Plus 256GB',
      'iPhone 14 128GB',
      'iPhone 13 Pro Max 256GB',
      'iPhone 13 Pro 128GB',
      'iPhone 13 256GB',
      'iPhone 13 128GB',
      'iPhone 12 Pro Max 256GB',
      'iPhone 12 Pro 128GB'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以接收从上一页传递的参数
    if (options.activeTab) {
      this.setData({
        activeTab: parseInt(options.activeTab)
      });
    }
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeTab: index,
      selectedModel: '', // 切换标签时清空选择
      showDropdown: false
    });
  },

  /**
   * 切换下拉框显示状态
   */
  toggleDropdown() {
    this.setData({
      showDropdown: !this.data.showDropdown
    });
  },

  /**
   * 选择型号
   */
  selectModel(e) {
    const model = e.currentTarget.dataset.model;
    this.setData({
      selectedModel: model,
      showDropdown: false
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生成报价单
   */
  generateQuote() {
    const { activeTab, selectedModel, tabList } = this.data;
    
    if (!selectedModel) {
      wx.showToast({
        title: '请先选择型号',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 这里可以调用云函数生成报价单
    wx.showLoading({
      title: '生成中...'
    });

    // 模拟生成过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '报价单生成成功',
        icon: 'success',
        duration: 2000
      });
      
      // 可以跳转到结果页面或返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }, 1500);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})
