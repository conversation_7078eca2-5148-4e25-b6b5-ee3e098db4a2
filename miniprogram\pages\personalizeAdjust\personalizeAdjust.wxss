/* pages/personalizeAdjust/personalizeAdjust.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  display: flex;
  flex-direction: column;
}

/* 顶部标签栏 */
.tab-container {
  display: flex;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.tab-item {
  flex: 1;
  padding: 25rpx 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: rgb(0, 0, 0);
  transition: all 0.3s ease;
  border-bottom: 3rpx solid transparent;
  position: relative;
}

.tab-item.active {
  color: #333;
  font-weight: 600;
  border-bottom-color: #333;
  background: #f8f8f8;
}

/* 选择型号区域 */
.model-selector {
  border-radius: 20rpx;
  display: flex;
  padding: 0 20rpx;
  white-space: nowrap;
  position: relative;
}

.selector-label {
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  
}

.dropdown-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 110rpx;
  border-radius: 10rpx;
  border: 2rpx solid #e0e0e0;
  position: absolute;
  right: 20rpx;
  top: -10rpx;
}

.dropdown-container:active {
  border-color: #333;
}

.dropdown-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}

.dropdown-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
  position: relative;
  left: 100rpx;
}

.dropdown-arrow.rotate {
  transform: rotate(180deg);
}

/* 下拉选项 */
.dropdown-options {
  position: absolute;
  top: calc(100% + 5rpx);
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 10rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
  z-index: 1000;
  max-height: 400rpx;
  overflow-y: auto;
  border: 1rpx solid #e0e0e0;
}

.dropdown-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  font-size: 26rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
  min-height: 80rpx;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:active {
  background: #f8f8f8;
}

/* 复选框样式 */
.checkbox-container {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #333;
  border-color: #333;
}

.checkmark {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.option-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}

/* 空状态显示 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 400rpx;
}

.empty-icon {
  margin-bottom: 30rpx;
}

.document-icon {
  width: 120rpx;
  height: 140rpx;
  background: #f0f0f0;
  border-radius: 15rpx;
  position: relative;
  border: 3rpx solid #ddd;
}

.doc-header {
  width: 60rpx;
  height: 20rpx;
  background: #333;
  border-radius: 4rpx;
  margin: 20rpx auto 15rpx;
}

.doc-line {
  width: 80rpx;
  height: 8rpx;
  background: #ccc;
  border-radius: 2rpx;
  margin: 8rpx auto;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  gap: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #f0f0f0;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: #e0e0e0;
}

.btn-primary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #333;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.btn-primary:active {
  background: #555;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .tab-item {
    font-size: 24rpx;
    padding: 20rpx 8rpx;
  }
  
  .selector-label {
    font-size: 26rpx;
  }
  
  .dropdown-text {
    font-size: 24rpx;
  }
  
  .empty-text {
    font-size: 26rpx;
  }
}

@media screen and (min-width: 414px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}
