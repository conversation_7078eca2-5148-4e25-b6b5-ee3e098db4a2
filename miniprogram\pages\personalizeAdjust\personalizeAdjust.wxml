<!--pages/personalizeAdjust/personalizeAdjust.wxml-->
<view class="container">
  <!-- 顶部标签栏 -->
  <view class="tab-container">
    <view wx:for="{{tabList}}" wx:key="index" 
          class="tab-item {{activeTab === index ? 'active' : ''}}"
          bindtap="switchTab"
          data-index="{{index}}">
      {{item}}
    </view>
  </view>

  <!-- 选择型号区域 -->
  <view class="model-selector">
    <text class="selector-label">选择型号：</text>
    <view class="dropdown-container" bindtap="toggleDropdown">
      <text class="dropdown-text">{{selectedModel || '点击此处选择需要调整的型号'}}</text>
      <text class="dropdown-arrow {{showDropdown ? 'rotate' : ''}}">▼</text>
    </view>
    
    <!-- 下拉选项 -->
    <view wx:if="{{showDropdown}}" class="dropdown-options">
      <view wx:for="{{modelOptions}}" wx:key="index"
            class="dropdown-option"
            bindtap="selectModel"
            data-model="{{item}}">
        {{item}}
      </view>
    </view>
  </view>

  <!-- 空状态显示 -->
  <view class="empty-state">
    <view class="empty-icon">
      <view class="document-icon">
        <view class="doc-header"></view>
        <view class="doc-line"></view>
        <view class="doc-line"></view>
        <view class="doc-line"></view>
      </view>
    </view>
    <text class="empty-text">如无需个性化调整，点击生成即可</text>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <view class="btn-secondary" bindtap="goBack">返回</view>
    <view class="btn-primary" bindtap="generateQuote">生成报价单</view>
  </view>
</view>
