/* pages/quoteDetail/quoteDetail.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 10rpx;
  height: 1750rpx;
}

/* 顶部标题 */
.header {
  text-align: center;
  padding: 42rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 水印输入区域 */
.watermark-section {
  background: c;
  border-radius: 20rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  white-space: nowrap;
}

.watermark-input {
  height: 60rpx;
  width: 750rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
  border: 1rpx solid rgb(216, 212, 212);
  background:#ffffff;
}

/* 颜色选择区域 */
.color-section {
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.color-row {
  display: flex;
  align-items: flex-start;
}

.color-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15rpx;
  
}

.color-item {
  border-radius: 10rpx;
  font-size: 24rpx;
  text-align: center;
  border: 3rpx solid transparent;
  color: #fff;
  font-weight: 500;
  text-shadow: 1rpx 1rpx 2rpx rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  min-height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 140rpx;
}

/* 商务白特殊字体颜色 */
.color-item.white-text {
  color: #333;
  text-shadow: none;
}

/* 选中状态的对勾标识 */
.color-item.selected::after {
  content: '✓';
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 30rpx;
  height: 30rpx;
  background: #333;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

/* 手机型号列表 */
.phone-list {
  padding: 0rpx 20rpx;
  margin-top: -20rpx;
}

.phone-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  border: 2rpx solid transparent;
  transition: border-color 0.3s ease;
}

.phone-item.selected {
  border-color: #333;
}

.delete-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4444;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  z-index: 10;
}

/* 选择框样式 */
.checkbox-container {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  z-index: 10;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #333;
  border-color: #333;
}

.checkmark {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.phone-info {
  padding: 40rpx;
}

.phone-row {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.phone-row:last-child {
  margin-bottom: 0;
}

.phone-label {
  font-size: 28rpx;
  color: #333;
  width: 160rpx;
  white-space: nowrap;
  text-align: end;
}

.phone-model {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  background: #f0f0f0;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  background: #f5f5f5;
  text-align: center;
}

.condition-selector {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  border: 1rpx solid rgb(216, 212, 212);
  position: relative;
  cursor: pointer;
}

.condition-text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #999;
}

/* 下拉选项样式 */
.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  z-index: 100;
  margin-top: 5rpx;
}

.dropdown-option {
  padding: 10rpx;
  font-size: 25rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: #f8f8f8;
}

.price-row {
  display: flex;
  align-items: center;
  margin-left: 160rpx;
}

.price-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  font-size: 32rpx;
  text-align: center;
  margin-right: 15rpx;
  border: 1rpx solid rgb(216, 212, 212);
  background: #ffffff;
}

.currency {
  font-size: 28rpx;
  color: #333;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  gap: 20rpx;
}

.btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #f0f0f0;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.btn-primary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #333;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}
